'use client';

import React, { useState, useCallback } from 'react';

import { Button } from '@/components/ui/button';

import SimpleCertificateForm from './SimpleCertificateForm';
import CertificatePreview from './CertificatePreview';
import { CertificateData, CertificateTemplate } from '@/types/certificate';
import { getDefaultTemplate } from '@/lib/certificate-templates';
import { Download, Loader2, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { certificateAnalytics } from '@/lib/analytics';
import { generateCertificatePDF } from '@/lib/pdf-generator';
import { Progress } from '@/components/ui/progress';
import { SlideIn, ScaleIn } from '@/components/ui/animations';

interface CertificateMakerProps {
  selectedTemplate?: CertificateTemplate;
  templates?: CertificateTemplate[];
  onTemplateChange?: (template: CertificateTemplate) => void;
}

export default function CertificateMaker({
  selectedTemplate: initialTemplate,
  templates = [],
  onTemplateChange
}: CertificateMakerProps) {
  const { toast } = useToast();
  // 直接使用传入的模板，不再支持模板切换
  const selectedTemplate = initialTemplate || getDefaultTemplate();

  const [formData, setFormData] = useState<CertificateData>({
    templateId: getDefaultTemplate().id,
    recipientName: '',
    date: '',
    signature: '',
    details: '',
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);



  const handleFormDataChange = useCallback((data: Partial<CertificateData>) => {
    setFormData(prev => ({
      ...prev,
      ...data,
    }));
  }, []);

  const handleGeneratePDF = useCallback(async () => {
    if (!selectedTemplate || !formData.recipientName || !formData.date || !formData.signature || !formData.details) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields before generating the certificate.",
        variant: "destructive",
      });

      // 跟踪错误
      certificateAnalytics.errorOccurred('validation_error', 'Missing required fields', 'pdf_generation');
      return;
    }

    // 跟踪生成开始
    certificateAnalytics.certificateGenerationStarted(selectedTemplate.id, formData);

    const startTime = Date.now();
    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      // 生成并下载PDF
      await generateCertificatePDF(selectedTemplate, formData);

      clearInterval(progressInterval);
      setGenerationProgress(100);

      const endTime = Date.now();
      const generationTime = endTime - startTime;

      // 跟踪生成完成
      certificateAnalytics.certificateGenerated(selectedTemplate.id, generationTime);

      toast({
        title: "Certificate Generated!",
        description: "Your certificate has been generated and downloaded successfully.",
        variant: "default",
      });
    } catch (error) {
      console.error('Error generating PDF:', error);

      // 跟踪错误
      certificateAnalytics.errorOccurred('generation_error', error instanceof Error ? error.message : 'Unknown error', 'pdf_generation');

      toast({
        title: "Generation Failed",
        description: "There was an error generating your certificate. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  }, [selectedTemplate, formData, toast]);

  const isFormValid = formData.recipientName && formData.date && formData.signature && formData.details;

  return (
    <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* 单列布局 - 参考 Bannerbear 设计 */}
      <div className="space-y-6 sm:space-y-8">


        {/* 表单和预览 - 统一的左右布局 */}
        <div className="space-y-6 sm:space-y-8">
          {/* 竖向模板的模板选择器 */}
          {selectedTemplate.orientation === 'portrait' && templates.length > 1 && (
            <SlideIn direction="up" delay={100}>
              <div className="text-center">
                {/* <h3 className="text-2xl font-bold text-gray-900 mb-6">Choose Your Template</h3> */}
                {(() => {
                  const shouldShowCarousel = templates.length > 5;

                  if (!shouldShowCarousel) {
                    // 直接展示所有纵向模板（1-5个）
                    return (
                      <div className="flex justify-center">
                        <div className={`grid gap-3 sm:gap-4 ${
                          templates.length === 1 ? 'grid-cols-1 max-w-xs' :
                          templates.length === 2 ? 'grid-cols-2 max-w-2xl' :
                          templates.length === 3 ? 'grid-cols-2 sm:grid-cols-3 max-w-3xl' :
                          templates.length === 4 ? 'grid-cols-2 sm:grid-cols-4 max-w-4xl' :
                          'grid-cols-2 sm:grid-cols-3 md:grid-cols-5 max-w-5xl'
                        }`}>
                          {templates.map((template) => (
                            <div
                              key={template.id}
                              className={`relative cursor-pointer rounded-lg border-2 transition-all duration-200 ${
                                selectedTemplate?.id === template.id
                                  ? 'border-blue-500 bg-blue-50'
                                  : 'border-gray-200 hover:border-gray-300'
                              } w-full max-w-xs touch-manipulation`}
                              onClick={() => onTemplateChange?.(template)}
                            >
                              <div className="p-2 sm:p-3">
                                <div className="bg-gray-50 rounded-lg overflow-hidden aspect-[3/4] mb-2 sm:mb-3">
                                  <img
                                    src={template.preview}
                                    alt={template.displayName}
                                    className="w-full h-full object-contain p-1"
                                    draggable={false}
                                  />
                                </div>
                                <div className="text-center">
                                  <h4 className="font-medium text-xs sm:text-sm text-gray-900 mb-1">
                                    {template.displayName}
                                  </h4>
                                  <p className="text-gray-600 text-xs line-clamp-2 hidden sm:block">
                                    {template.description}
                                  </p>
                                </div>
                                {selectedTemplate?.id === template.id && (
                                  <div className="absolute top-2 right-2">
                                    <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                                      <CheckCircle className="w-3 h-3 text-white" />
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  } else {
                    // 使用轮播组件（超过5个模板时）
                    const currentIndex = templates.findIndex(t => t.id === selectedTemplate?.id);
                    const templatesPerPage = 5; // 固定每页显示5个模板
                    const currentPage = Math.floor(currentIndex / templatesPerPage);
                    const totalPages = Math.ceil(templates.length / templatesPerPage);
                    const startIndex = currentPage * templatesPerPage;
                    const endIndex = Math.min(startIndex + templatesPerPage, templates.length);
                    const visibleTemplates = templates.slice(startIndex, endIndex);

                    const canGoPrev = currentPage > 0;
                    const canGoNext = currentPage < totalPages - 1;

                    const goToPrevPage = () => {
                      if (canGoPrev) {
                        const newIndex = (currentPage - 1) * templatesPerPage;
                        onTemplateChange?.(templates[newIndex]);
                      }
                    };

                    const goToNextPage = () => {
                      if (canGoNext) {
                        const newIndex = (currentPage + 1) * templatesPerPage;
                        onTemplateChange?.(templates[newIndex]);
                      }
                    };

                    return (
                      <div className="relative max-w-7xl mx-auto">
                        {/* 模板计数和导航信息 */}
                        <div className="text-center mb-4">
                          <p className="text-sm text-gray-600 mb-1">
                            {currentIndex + 1} of {templates.length} templates
                          </p>
                          <p className="text-xs text-gray-500">
                            Use ← → arrow keys or click to navigate
                          </p>
                        </div>

                        <div className="flex items-center justify-center space-x-4">
                          {/* 左导航按钮 */}
                          <button
                            onClick={goToPrevPage}
                            disabled={!canGoPrev}
                            className={`flex-shrink-0 p-2 sm:p-3 rounded-full shadow-md transition-all border min-h-[44px] min-w-[44px] flex items-center justify-center ${
                              canGoPrev
                                ? 'bg-white hover:shadow-lg border-gray-200 hover:border-gray-300 text-gray-600 hover:text-gray-800'
                                : 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
                            }`}
                            aria-label="Previous page"
                          >
                            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                            </svg>
                          </button>

                          {/* 模板网格 - 固定大小，每页5个 */}
                          <div className="flex-1 overflow-hidden">
                            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4 max-w-5xl mx-auto">
                              {visibleTemplates.map((template) => (
                                <div
                                  key={template.id}
                                  className={`relative cursor-pointer rounded-lg border-2 transition-all duration-200 ${
                                    selectedTemplate?.id === template.id
                                      ? 'border-blue-500 bg-blue-50'
                                      : 'border-gray-200 hover:border-gray-300'
                                  } w-full touch-manipulation`}
                                  onClick={() => onTemplateChange?.(template)}
                                >
                                  <div className="p-3">
                                    <div className="bg-gray-50 rounded-lg mb-3 overflow-hidden aspect-[3/4]">
                                      <img
                                        src={template.preview}
                                        alt={template.displayName}
                                        className="w-full h-full object-contain p-1"
                                        draggable={false}
                                      />
                                    </div>
                                    <div className="text-center">
                                      <h4 className="font-semibold text-sm text-gray-900 mb-1">
                                        {template.displayName}
                                      </h4>
                                      <p className="text-gray-600 text-xs line-clamp-2">
                                        {template.description}
                                      </p>
                                    </div>
                                    {selectedTemplate?.id === template.id && (
                                      <div className="absolute top-2 right-2">
                                        <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                                          <CheckCircle className="w-3 h-3 text-white" />
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* 右导航按钮 */}
                          <button
                            onClick={goToNextPage}
                            disabled={!canGoNext}
                            className={`flex-shrink-0 p-2 sm:p-3 rounded-full shadow-md transition-all border min-h-[44px] min-w-[44px] flex items-center justify-center ${
                              canGoNext
                                ? 'bg-white hover:shadow-lg border-gray-200 hover:border-gray-300 text-gray-600 hover:text-gray-800'
                                : 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
                            }`}
                            aria-label="Next page"
                          >
                            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </button>
                        </div>

                        {/* 页面指示器 */}
                        <div className="mt-6 text-center">
                          {totalPages > 1 && (
                            <div className="flex justify-center items-center space-x-2">
                              <span className="text-sm text-gray-600">
                                Page {currentPage + 1} of {totalPages}
                              </span>
                              <div className="flex space-x-1">
                                {Array.from({ length: totalPages }, (_, i) => (
                                  <button
                                    key={i}
                                    onClick={() => {
                                      const newIndex = i * templatesPerPage;
                                      onTemplateChange?.(templates[newIndex]);
                                    }}
                                    className={`w-2 h-2 rounded-full transition-all duration-200 ${
                                      i === currentPage
                                        ? 'bg-blue-500 scale-125'
                                        : 'bg-gray-300 hover:bg-gray-400'
                                    }`}
                                    aria-label={`Go to page ${i + 1}`}
                                  />
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  }
                })()}
              </div>
            </SlideIn>
          )}

          {/* 表单和预览的左右布局 - 横向和竖向模板使用相同布局 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
            {/* 左侧：表单 */}
            <SlideIn direction="left" delay={200}>
              <div className="space-y-4 sm:space-y-6">
                <SimpleCertificateForm
                  template={selectedTemplate}
                  formData={formData}
                  onFormDataChange={handleFormDataChange}
                />

                {/* 生成按钮 */}
                <div className="space-y-4">
                  <Button
                    onClick={handleGeneratePDF}
                    disabled={!isFormValid || isGenerating}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 sm:py-3 text-base sm:text-lg font-medium transition-all duration-200 hover:shadow-lg min-h-[48px] sm:min-h-[52px] touch-manipulation"
                    size="lg"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        Generating PDF... {generationProgress}%
                      </>
                    ) : (
                      <>
                        <Download className="mr-2 h-5 w-5" />
                        Create PDF
                      </>
                    )}
                  </Button>

                  {isGenerating && (
                    <ScaleIn>
                      <div className="space-y-2">
                        <Progress value={generationProgress} className="w-full" />
                        <p className="text-sm text-center text-gray-600">
                          Generating your certificate...
                        </p>
                      </div>
                    </ScaleIn>
                  )}
                </div>
              </div>
            </SlideIn>

            {/* 右侧：预览 */}
            <SlideIn direction="right" delay={400}>
              <div className="lg:sticky lg:top-8">
                <CertificatePreview
                  template={selectedTemplate}
                  formData={formData}
                />
              </div>
            </SlideIn>
          </div>
        </div>
      </div>


    </div>
  );
}
